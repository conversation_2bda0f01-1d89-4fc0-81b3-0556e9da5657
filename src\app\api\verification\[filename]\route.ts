/**
 * 搜索引擎验证文件 API 路由
 * 动态处理各种搜索引擎的验证文件请求
 */

import { handleVerificationFile } from '@/lib/seo/verification-files';
import { NextRequest, NextResponse } from 'next/server';

interface RouteContext {
  params: Promise<{
    filename: string;
  }>;
}

/**
 * GET 请求处理器
 * 处理搜索引擎验证文件请求
 */
export async function GET(request: NextRequest, { params }: RouteContext) {
  try {
    const { filename } = await params;

    // 验证文件名格式
    if (!filename || typeof filename !== 'string') {
      return new NextResponse('Invalid filename', { status: 400 });
    }

    // 处理验证文件请求
    const response = handleVerificationFile(filename);

    if (!response) {
      return new NextResponse('Verification file not found', { status: 404 });
    }

    return response;
  } catch (error) {
    console.error('Error handling verification file:', error);

    return new NextResponse('Internal Server Error', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
}
